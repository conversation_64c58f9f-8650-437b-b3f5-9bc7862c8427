using System;
using System.Collections.Generic;
using UnityEngine;

namespace Archery
{
    // Ability instance
    public class Ability
    {
        public AbilityConfig Config { get; private set; } // Ability configuration
        public Unit Owner { get; private set; } // The unit that owns this ability
        public float CooldownTime { get; private set; } // Time remaining for cooldown

        public bool Cast()
        {
            return false;
        }
    }
}