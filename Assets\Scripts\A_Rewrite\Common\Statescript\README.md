# Statescript System

A high-performance, extensible visual scripting system inspired by Overwatch's Statescript, designed for Unity with deterministic lockstep gameplay support.

## Overview

The Statescript system provides a node-based visual scripting solution with two main components:

- **Runtime**: Pure C# library that runs without Unity dependencies
- **Editor**: Unity integration for visual editing and asset management

## Features

- ✅ **High Performance**: Optimized for real-time gameplay with minimal allocations
- ✅ **Deterministic**: Compatible with lockstep networking and fixed-point math
- ✅ **Extensible**: Easy to add custom node types
- ✅ **Multiple Serialization**: JSON, Binary, and Compressed Binary formats
- ✅ **Unity Integration**: ScriptableObject assets and custom inspectors
- ✅ **Visual Node Editor**: Full-featured graph editor with drag-and-drop
- ✅ **Editor-Runtime Separation**: Editor data excluded from runtime builds
- ✅ **Entity Integration**: Component-based system for entities
- ✅ **Event System**: Event-driven node execution
- ✅ **Variable System**: Type-safe variables with validation

## Architecture

### Core Components

```
Common/Statescript/
├── Runtime/
│   ├── Core/
│   │   ├── StatescriptGraph.cs      # Main graph container
│   │   ├── StatescriptNode.cs       # Base node class + built-in nodes
│   │   ├── StatescriptConnection.cs # Node connections
│   │   ├── StatescriptVariable.cs   # Variable system
│   │   ├── StatescriptContext.cs    # Execution context
│   │   └── StatescriptRunner.cs     # Execution engine
│   ├── Nodes/
│   │   ├── FlowControlNodes.cs      # Flow control nodes
│   │   └── EventNodes.cs            # Event-based nodes
│   └── Serialization/
│       └── StatescriptSerializer.cs # Serialization system
├── Editor.Unity/
│   ├── StatescriptAsset.cs          # Unity ScriptableObject
│   ├── StatescriptAssetInspector.cs # Custom inspector
│   ├── StatescriptEditorData.cs     # Editor-only data structures
│   ├── StatescriptGraphEditor.cs    # Visual node graph editor
│   └── StatescriptNodePropertyEditor.cs # Node property editor
└── Examples/
    └── StatescriptExample.cs        # Usage examples
```

## Quick Start

### 1. Basic Usage

```csharp
// Create a simple graph
var graph = new StatescriptGraph("My Graph");

// Add nodes
var entryNode = new EntryNode { Id = 1, Name = "Entry" };
var logNode = new LogNode { Id = 2, Name = "Log Message" };
logNode.SetProperty("Message", "Hello, Statescript!");

graph.AddNode(entryNode);
graph.AddNode(logNode);

// Connect nodes
graph.AddConnection(new StatescriptConnection(1, 2));

// Execute
var context = new StatescriptContext();
graph.Initialize(context);
graph.Start();

// Update each frame
while (graph.IsRunning)
{
    graph.Update(Time.deltaTime);
}
```

### 2. Entity Integration

```csharp
// Add StatescriptComponent to an entity
var entity = entityManager.CreateEntity();
var statescriptComponent = entity.AddComponent<StatescriptComponent>();

// Load and run a graph
statescriptComponent.LoadGraphFromFile("MyGraph", "path/to/graph.bin");
statescriptComponent.StartGraph("MyGraph");

// The component will automatically update the graph each frame
```

### 3. Unity Asset Creation

1. Right-click in Project window
2. Create → Statescript → Statescript Graph
3. Use the custom inspector to create and manage graphs
4. Click "Open Graph Editor" to visually edit the graph
5. Load in runtime using `StatescriptAsset.LoadGraph()`

### 4. Visual Graph Editor

```csharp
// Open the graph editor window
StatescriptGraphEditor.OpenWindow();

// Or open a specific asset
StatescriptGraphEditor.OpenAsset(myStatescriptAsset);
```

**Editor Features:**
- **Drag & Drop**: Move nodes around the canvas
- **Zoom & Pan**: Navigate large graphs easily
- **Grid Snapping**: Align nodes perfectly
- **Connection Creation**: Click and drag between node ports
- **Property Editing**: Double-click nodes to edit properties
- **Context Menu**: Right-click to add new nodes
- **Auto-Save**: Automatically saves changes
- **Multi-Selection**: Select multiple nodes with Ctrl+Click
- **Keyboard Shortcuts**:
  - `Delete` - Delete selected nodes
  - `F` - Focus on graph
  - `Ctrl+S` - Save graph

## Node Types

### Built-in Nodes

| Node Type | Description | Properties |
|-----------|-------------|------------|
| `EntryNode` | Graph entry point | None |
| `LogNode` | Debug logging | Message |
| `WaitNode` | Time delay | Duration |
| `SequenceNode` | Execute nodes in order | None |
| `BranchNode` | Conditional execution | Condition |
| `ParallelNode` | Execute nodes simultaneously | ExecutionMode |
| `LoopNode` | Repeat execution | Type, MaxIterations |
| `ConditionNode` | Evaluate conditions | Type, Operands, Operator |
| `GetVariableNode` | Read variable value | VariableName |
| `SetVariableNode` | Write variable value | VariableName, NewValue |
| `EventTriggerNode` | Wait for events | EventName, AutoReset |
| `FireEventNode` | Send events | EventName, EventDataVariable |

### Creating Custom Nodes

```csharp
[MemoryPackable]
public sealed partial class MyCustomNode : StatescriptNode
{
    public MyCustomNode() : base(StatescriptNodeType.Action)
    {
        Name = "My Custom Node";
    }

    protected override void OnExecute()
    {
        // Your custom logic here
        var myProperty = GetProperty<string>("MyProperty", "default");

        // Complete execution
        CompleteExecution(true);
    }
}

// Don't forget to add to the MemoryPackUnion list in StatescriptNode.cs
```

## Serialization

### Formats

- **JSON**: Human-readable, good for debugging and version control
- **Binary**: Compact and fast, best for runtime performance
- **Compressed Binary**: Smallest size, good for network transmission

### Usage

```csharp
// Serialize to different formats
var jsonData = StatescriptSerializer.Serialize(graph, StatescriptSerializationFormat.Json);
var binaryData = StatescriptSerializer.Serialize(graph, StatescriptSerializationFormat.Binary);

// Deserialize
var graph = StatescriptSerializer.Deserialize(binaryData, StatescriptSerializationFormat.Binary);

// File operations
StatescriptSerializer.SerializeToFile(graph, "graph.bin");
var loadedGraph = StatescriptSerializer.DeserializeFromFile("graph.bin");

// Auto-detect format by extension
StatescriptSerializer.SerializeToFileAuto(graph, "graph.json"); // Uses JSON
var autoGraph = StatescriptSerializer.DeserializeFromFileAuto("graph.json");
```

## Variables

### Supported Types

- `Boolean`
- `Integer`
- `Float`
- `String`
- `Vector2` (FVector2)
- `Vector3` (FVector3)
- `Entity`
- `Object` (any type)

### Usage

```csharp
// Add variables to graph
var healthVar = new StatescriptVariable("Health", StatescriptVariableType.Integer, 100);
graph.Variables.Add(healthVar);

// Access in runtime
graph.SetVariableValue("Health", 75);
var health = graph.GetVariableValue<int>("Health");
```

## Events

### Event System Integration

```csharp
// Fire events from code
statescriptComponent.FireEvent("PlayerDamaged", damageAmount);

// Use EventTriggerNode to respond to events
var eventNode = new EventTriggerNode();
eventNode.EventName = "PlayerDamaged";
// Node will execute when event is fired
```

## Performance Considerations

1. **Use Binary Serialization** for runtime assets
2. **Pool Node Instances** for frequently created/destroyed graphs
3. **Limit Graph Complexity** - avoid deep nesting and excessive nodes
4. **Cache Variable Lookups** in custom nodes
5. **Use Fixed-Point Math** for deterministic calculations

## Integration with Existing Systems

### Entity-Component System
- `StatescriptComponent` integrates with your entity system
- Access entity components through `StatescriptContext`

### Event System
- Uses your existing `EventBus` implementation
- Fire and listen to events from nodes

### Command System
- Execute commands through `StatescriptContext.ExecuteCommand()`

### Fixed-Point Math
- Compatible with `Fix64` and `FVector` types
- Deterministic execution for lockstep networking

## Examples

See `Common/Statescript/Examples/StatescriptExample.cs` for complete examples including:

- Simple sequential execution
- Conditional branching
- Looping constructs
- Serialization demonstrations

## Future Enhancements

- [ ] Visual node editor window in Unity
- [ ] Code generation for maximum performance
- [ ] More built-in node types (math, string operations, etc.)
- [ ] Debugging and profiling tools
- [ ] Hot-reloading of graphs in runtime
- [ ] Graph templates and presets

## License

This system is designed to integrate with your existing codebase and follows the same licensing terms as your project.
