using System;
using System.Collections.Generic;
using MemoryPack;
using MemoryPack.Formatters;

namespace GP.Common.Statescript
{
    [MemoryPackable(GenerateType.NoGenerate)]
    public partial interface IVariable
    {
        bool Set(IVariable other);
        IVariable Clone();
    }

    [MemoryPackable]
    public partial class Variable<T> : IVariable, IComparable<Variable<T>>, IEquatable<Variable<T>>
    {
        public T Value { get; set; }


        public Variable(T value)
        {
            Value = value;
        }

        public bool Set(IVariable other)
        {
            if (other is Variable<T> variable)
            {
                Value = variable.Value;
                return true;
            }
            return false;
        }

        public IVariable Clone()
        {
            return new Variable<T>(Value);
        }

        public override string ToString()
        {
            return Value?.ToString() ?? "null";
        }

        public int CompareTo(Variable<T> other)
        {
            if (other == null) return 1;
            if (Value is IComparable<T> comparable)
            {
                return comparable.CompareTo(other.Value);
            }
            return 0; // Default comparison if T does not implement IComparable
        }

        public bool Equals(Variable<T> other)
        {
            if (other == null) return false;
            return EqualityComparer<T>.Default.Equals(Value, other.Value);
        }

        public override int GetHashCode()
        {
            return Value?.GetHashCode() ?? 0;
        }

        // implicit & explicit operators
        public static implicit operator Variable<T>(T value)
        {
            return new Variable<T>(value);
        }

        public static explicit operator T(Variable<T> variable)
        {
            return variable.Value;
        }
    }

    public static class VariableSerializationSetup
    {
        [UnityEngine.RuntimeInitializeOnLoadMethod(UnityEngine.RuntimeInitializeLoadType.BeforeSceneLoad)]
        public static void RegisterVariableTypes()
        {
            // Register the dynamic union formatter for IVariable
            var formatter = new DynamicUnionFormatter<IVariable>(
                (0, typeof(Variable<bool>)),
                (1, typeof(Variable<int>)),
                (2, typeof(Variable<float>)),
                (3, typeof(Variable<string>)),
                (4, typeof(Variable<UnityEngine.Vector2>)),
                (5, typeof(Variable<UnityEngine.Vector3>)),
                (6, typeof(Variable<UnityEngine.Quaternion>))
            );

            // Register the formatter
            MemoryPackFormatterProvider.Register(formatter);
        }
    }

    /// <summary>
    /// Base class for all Statescript variables
    /// </summary>
    [MemoryPackable]
    public partial class StatescriptVariable
    {
        [MemoryPackInclude]
        public string Name { get; set; } = string.Empty;
        
        [MemoryPackInclude]
        public string Description { get; set; } = string.Empty;
        
        [MemoryPackInclude]
        public bool IsExposed { get; set; } = false;

        [MemoryPackInclude]
        public IVariable DefaultValue { get; set; } = null;

        [MemoryPackIgnore]
        public IVariable Value { get; set; }

        [MemoryPackConstructor]
        public StatescriptVariable()
        {
            ResetToDefault();
        }

        public StatescriptVariable(string name)
        {
            Name = name;
            ResetToDefault();
        }

        public T GetValue<T>()
        {
            if (Value is Variable<T> variable)
            {
                return variable.Value;
            }
            throw new InvalidCastException($"Cannot cast variable '{Name}' to type {typeof(T)}");
        }

        public bool TryGetValue<T>(out T value)
        {
            if (Value is Variable<T> variable)
            {
                value = variable.Value;
                return true;
            }
            value = default;
            return false;
        }

        public void SetValue<T>(T newValue)
        {
            if (Value is Variable<T> variable)
            {
                variable.Value = newValue;
            }
            else
            {
                throw new InvalidCastException($"Cannot cast variable '{Name}' to type {typeof(T)}");
            }
        }

        /// <summary>
        /// Reset to default value
        /// </summary>
        public void ResetToDefault()
        {
            if (DefaultValue != null)
            {
                Value = DefaultValue.Clone();
            }
        }

        public override string ToString()
        {
            return $"{Name}: {Value?.ToString() ?? "null"}";
        }
    }

    /// <summary>
    /// Variable nodes for getting and setting variables
    /// </summary>
    [MemoryPackable]
    public sealed partial class GetVariableNode : StatescriptNode
    {
        [MemoryPackInclude]
        public string VariableName { get; set; } = string.Empty;

        public GetVariableNode() : base(StatescriptNodeType.Variable)
        {
            Name = "Get Variable";
        }

        protected override void OnExecute()
        {
            var variable = Graph?.GetVariable(VariableName);
            if (variable != null)
            {
                // Store the value in the node's output for other nodes to access
                SetProperty("OutputValue", variable);
                CompleteExecution(true);
            }
            else
            {
                Context?.LogError($"Variable '{VariableName}' not found");
                CompleteExecution(false);
            }
        }

        public override List<string> Validate()
        {
            var errors = base.Validate();
            
            if (string.IsNullOrEmpty(VariableName))
            {
                errors.Add($"Get Variable node '{Name}' must specify a variable name");
            }
            
            return errors;
        }
    }

    /// <summary>
    /// Node for setting variable values
    /// </summary>
    [MemoryPackable]
    public sealed partial class SetVariableNode : StatescriptNode
    {
        [MemoryPackInclude]
        public string VariableName { get; set; } = string.Empty;

        [MemoryPackInclude]
        public IVariable NewValue { get; set; } = null;

        public SetVariableNode() : base(StatescriptNodeType.Variable)
        {
            Name = "Set Variable";
        }

        protected override void OnExecute()
        {
            var variable = Graph?.GetVariable(VariableName);
            if (variable != null)
            {
                if (variable.Value.Set(NewValue))
                {
                    // Successfully set the variable, complete execution
                    CompleteExecution(true);
                }
                else
                {
                    Context?.LogError($"Failed to set variable '{VariableName}' - invalid value type");
                    CompleteExecution(false);
                }
            }
            else
            {
                Context?.LogError($"Variable '{VariableName}' not found");
                CompleteExecution(false);
            }
        }

        public override List<string> Validate()
        {
            var errors = base.Validate();
            
            if (string.IsNullOrEmpty(VariableName))
            {
                errors.Add($"Set Variable node '{Name}' must specify a variable name");
            }
            
            return errors;
        }
    }
}
