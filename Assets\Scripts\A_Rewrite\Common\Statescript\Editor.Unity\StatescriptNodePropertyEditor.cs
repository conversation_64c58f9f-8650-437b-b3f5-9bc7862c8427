#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using GP.Common.Statescript;

namespace GP.Common.Statescript.Editor
{
    /// <summary>
    /// Property editor for Statescript nodes
    /// </summary>
    public class StatescriptNodePropertyEditor : EditorWindow
    {
        private StatescriptNode _selectedNode;
        private StatescriptGraph _currentGraph;
        private Vector2 _scrollPosition;
        private bool _hasChanges = false;

        public static void ShowEditor(StatescriptNode node, StatescriptGraph graph)
        {
            var window = GetWindow<StatescriptNodePropertyEditor>("Node Properties");
            window._selectedNode = node;
            window._currentGraph = graph;
            window._hasChanges = false;
            window.Show();
        }

        private void OnGUI()
        {
            if (_selectedNode == null)
            {
                EditorGUILayout.HelpBox("No node selected", MessageType.Info);
                return;
            }

            EditorGUILayout.Space();
            
            // Header
            EditorGUILayout.LabelField($"Node Properties: {_selectedNode.Name}", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);

            // Basic properties
            DrawBasicProperties();
            EditorGUILayout.Space();

            // Node-specific properties
            DrawNodeSpecificProperties();
            EditorGUILayout.Space();

            // Custom properties
            DrawCustomProperties();

            EditorGUILayout.EndScrollView();

            // Buttons
            EditorGUILayout.Space();
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("Apply") && _hasChanges)
            {
                ApplyChanges();
            }
            
            if (GUILayout.Button("Reset"))
            {
                _hasChanges = false;
                Repaint();
            }
            
            if (GUILayout.Button("Close"))
            {
                Close();
            }
            
            EditorGUILayout.EndHorizontal();

            if (_hasChanges)
            {
                EditorGUILayout.HelpBox("Properties have been modified. Click Apply to save changes.", MessageType.Warning);
            }
        }

        private void DrawBasicProperties()
        {
            EditorGUILayout.LabelField("Basic Properties", EditorStyles.boldLabel);
            
            EditorGUI.BeginChangeCheck();
            
            var newName = EditorGUILayout.TextField("Name", _selectedNode.Name);
            if (newName != _selectedNode.Name)
            {
                _selectedNode.Name = newName;
                _hasChanges = true;
            }
            
            var newDescription = EditorGUILayout.TextField("Description", _selectedNode.Description);
            if (newDescription != _selectedNode.Description)
            {
                _selectedNode.Description = newDescription;
                _hasChanges = true;
            }
            
            EditorGUILayout.LabelField("Type", _selectedNode.NodeType.ToString());
            EditorGUILayout.LabelField("ID", _selectedNode.Id.ToString());
            
            if (Application.isPlaying)
            {
                EditorGUILayout.LabelField("State", _selectedNode.State.ToString());
                EditorGUILayout.LabelField("Is Active", _selectedNode.IsActive.ToString());
            }
        }

        private void DrawNodeSpecificProperties()
        {
            EditorGUILayout.LabelField("Node-Specific Properties", EditorStyles.boldLabel);
            
            switch (_selectedNode)
            {
                case WaitNode waitNode:
                    DrawWaitNodeProperties(waitNode);
                    break;
                    
                case LogNode logNode:
                    DrawLogNodeProperties(logNode);
                    break;
                    
                case BranchNode branchNode:
                    DrawBranchNodeProperties(branchNode);
                    break;
                    
                case ConditionNode conditionNode:
                    DrawConditionNodeProperties(conditionNode);
                    break;
                    
                case LoopNode loopNode:
                    DrawLoopNodeProperties(loopNode);
                    break;
                    
                case GetVariableNode getVarNode:
                    DrawGetVariableNodeProperties(getVarNode);
                    break;
                    
                case SetVariableNode setVarNode:
                    DrawSetVariableNodeProperties(setVarNode);
                    break;
                    
                case EventTriggerNode eventNode:
                    DrawEventTriggerNodeProperties(eventNode);
                    break;
                    
                case FireEventNode fireEventNode:
                    DrawFireEventNodeProperties(fireEventNode);
                    break;
                    
                case ParallelNode parallelNode:
                    DrawParallelNodeProperties(parallelNode);
                    break;
                    
                default:
                    EditorGUILayout.LabelField("No specific properties for this node type.");
                    break;
            }
        }

        private void DrawWaitNodeProperties(WaitNode node)
        {
            var duration = node.GetProperty<float>("Duration", 1.0f);
            var newDuration = EditorGUILayout.FloatField("Duration (seconds)", duration);
            if (newDuration != duration)
            {
                node.SetProperty("Duration", newDuration);
                _hasChanges = true;
            }
        }

        private void DrawLogNodeProperties(LogNode node)
        {
            var message = node.GetProperty<string>("Message", "Log message");
            var newMessage = EditorGUILayout.TextField("Message", message);
            if (newMessage != message)
            {
                node.SetProperty("Message", newMessage);
                _hasChanges = true;
            }
        }

        private void DrawBranchNodeProperties(BranchNode node)
        {
            var condition = node.GetProperty<bool>("Condition", false);
            var newCondition = EditorGUILayout.Toggle("Condition", condition);
            if (newCondition != condition)
            {
                node.SetProperty("Condition", newCondition);
                _hasChanges = true;
            }
        }

        private void DrawConditionNodeProperties(ConditionNode node)
        {
            var type = node.Type;
            var newType = (ConditionType)EditorGUILayout.EnumPopup("Type", type);
            if (newType != type)
            {
                node.Type = newType;
                _hasChanges = true;
            }
            
            var leftOperand = node.LeftOperand;
            var newLeftOperand = EditorGUILayout.TextField("Left Operand", leftOperand);
            if (newLeftOperand != leftOperand)
            {
                node.LeftOperand = newLeftOperand;
                _hasChanges = true;
            }
            
            var rightOperand = node.RightOperand;
            var newRightOperand = EditorGUILayout.TextField("Right Operand", rightOperand);
            if (newRightOperand != rightOperand)
            {
                node.RightOperand = newRightOperand;
                _hasChanges = true;
            }
            
            var op = node.Operator;
            var newOp = (ComparisonOperator)EditorGUILayout.EnumPopup("Operator", op);
            if (newOp != op)
            {
                node.Operator = newOp;
                _hasChanges = true;
            }
        }

        private void DrawLoopNodeProperties(LoopNode node)
        {
            var type = node.Type;
            var newType = (LoopType)EditorGUILayout.EnumPopup("Loop Type", type);
            if (newType != type)
            {
                node.Type = newType;
                _hasChanges = true;
            }
            
            if (type == LoopType.Count)
            {
                var maxIterations = node.MaxIterations;
                var newMaxIterations = EditorGUILayout.IntField("Max Iterations", maxIterations);
                if (newMaxIterations != maxIterations)
                {
                    node.MaxIterations = newMaxIterations;
                    _hasChanges = true;
                }
            }
            
            if (type == LoopType.While)
            {
                var conditionVariable = node.ConditionVariable;
                var newConditionVariable = DrawVariableSelector("Condition Variable", conditionVariable);
                if (newConditionVariable != conditionVariable)
                {
                    node.ConditionVariable = newConditionVariable;
                    _hasChanges = true;
                }
            }
        }

        private void DrawGetVariableNodeProperties(GetVariableNode node)
        {
            var variableName = node.VariableName;
            var newVariableName = DrawVariableSelector("Variable Name", variableName);
            if (newVariableName != variableName)
            {
                node.VariableName = newVariableName;
                _hasChanges = true;
            }
        }

        private void DrawSetVariableNodeProperties(SetVariableNode node)
        {
            var variableName = node.VariableName;
            var newVariableName = DrawVariableSelector("Variable Name", variableName);
            if (newVariableName != variableName)
            {
                node.VariableName = newVariableName;
                _hasChanges = true;
            }
            
            // Show current value for editing
            var variable = _currentGraph?.GetVariable(variableName);
            if (variable != null)
            {
                EditorGUILayout.LabelField("Variable Type", variable.Type.ToString());
                
                var newValue = DrawVariableValueEditor("New Value", variable.Type, node.NewValue);
                if (!Equals(newValue, node.NewValue))
                {
                    node.NewValue = newValue;
                    _hasChanges = true;
                }
            }
        }

        private void DrawEventTriggerNodeProperties(EventTriggerNode node)
        {
            var eventName = node.EventName;
            var newEventName = EditorGUILayout.TextField("Event Name", eventName);
            if (newEventName != eventName)
            {
                node.EventName = newEventName;
                _hasChanges = true;
            }
            
            var autoReset = node.AutoReset;
            var newAutoReset = EditorGUILayout.Toggle("Auto Reset", autoReset);
            if (newAutoReset != autoReset)
            {
                node.AutoReset = newAutoReset;
                _hasChanges = true;
            }
        }

        private void DrawFireEventNodeProperties(FireEventNode node)
        {
            var eventName = node.EventName;
            var newEventName = EditorGUILayout.TextField("Event Name", eventName);
            if (newEventName != eventName)
            {
                node.EventName = newEventName;
                _hasChanges = true;
            }
            
            var eventDataVariable = node.EventDataVariable;
            var newEventDataVariable = DrawVariableSelector("Event Data Variable", eventDataVariable);
            if (newEventDataVariable != eventDataVariable)
            {
                node.EventDataVariable = newEventDataVariable;
                _hasChanges = true;
            }
        }

        private void DrawParallelNodeProperties(ParallelNode node)
        {
            var executionMode = node.ExecutionMode;
            var newExecutionMode = (ParallelExecutionMode)EditorGUILayout.EnumPopup("Execution Mode", executionMode);
            if (newExecutionMode != executionMode)
            {
                node.ExecutionMode = newExecutionMode;
                _hasChanges = true;
            }
        }

        private string DrawVariableSelector(string label, string currentValue)
        {
            if (_currentGraph == null || _currentGraph.Variables.Count == 0)
            {
                return EditorGUILayout.TextField(label, currentValue);
            }
            
            var variableNames = _currentGraph.Variables.Select(v => v.Name).ToArray();
            var currentIndex = Array.IndexOf(variableNames, currentValue);
            if (currentIndex < 0) currentIndex = 0;
            
            var newIndex = EditorGUILayout.Popup(label, currentIndex, variableNames);
            return newIndex >= 0 && newIndex < variableNames.Length ? variableNames[newIndex] : currentValue;
        }

        private object DrawVariableValueEditor(string label, StatescriptVariableType type, object currentValue)
        {
            return type switch
            {
                StatescriptVariableType.Boolean => EditorGUILayout.Toggle(label, currentValue is bool b ? b : false),
                StatescriptVariableType.Integer => EditorGUILayout.IntField(label, currentValue is int i ? i : 0),
                StatescriptVariableType.Float => EditorGUILayout.FloatField(label, currentValue is float f ? f : 0f),
                StatescriptVariableType.String => EditorGUILayout.TextField(label, currentValue as string ?? ""),
                // StatescriptVariableType.Vector2 => EditorGUILayout.Vector2Field(label, currentValue is FVector2 v2 ? new Vector2((float)v2.x, (float)v2.y) : Vector2.zero),
                // StatescriptVariableType.Vector3 => EditorGUILayout.Vector3Field(label, currentValue is FVector3 v3 ? new Vector3((float)v3.x, (float)v3.y, (float)v3.z) : Vector3.zero),
                _ => EditorGUILayout.TextField(label, currentValue?.ToString() ?? "")
            };
        }

        private void DrawCustomProperties()
        {
            EditorGUILayout.LabelField("Custom Properties", EditorStyles.boldLabel);
            
            if (_selectedNode.Properties.Count == 0)
            {
                EditorGUILayout.LabelField("No custom properties");
                return;
            }
            
            var keysToRemove = new List<string>();
            var propertiesToUpdate = new Dictionary<string, object>();
            
            foreach (var kvp in _selectedNode.Properties)
            {
                EditorGUILayout.BeginHorizontal();
                
                EditorGUILayout.LabelField(kvp.Key, GUILayout.Width(100));
                
                var valueStr = kvp.Value?.ToString() ?? "";
                var newValueStr = EditorGUILayout.TextField(valueStr);
                
                if (newValueStr != valueStr)
                {
                    propertiesToUpdate[kvp.Key] = newValueStr;
                    _hasChanges = true;
                }
                
                if (GUILayout.Button("X", GUILayout.Width(20)))
                {
                    keysToRemove.Add(kvp.Key);
                    _hasChanges = true;
                }
                
                EditorGUILayout.EndHorizontal();
            }
            
            // Apply updates
            foreach (var kvp in propertiesToUpdate)
            {
                _selectedNode.SetProperty(kvp.Key, kvp.Value);
            }
            
            // Remove properties
            foreach (var key in keysToRemove)
            {
                _selectedNode.Properties.Remove(key);
            }
            
            // Add new property
            EditorGUILayout.Space();
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("Add Property"))
            {
                _selectedNode.SetProperty("NewProperty", "Value");
                _hasChanges = true;
            }
            
            EditorGUILayout.EndHorizontal();
        }

        private void ApplyChanges()
        {
            _hasChanges = false;
            EditorUtility.SetDirty(_selectedNode as UnityEngine.Object);
            Debug.Log($"Applied changes to node: {_selectedNode.Name}");
        }
    }
}
#endif
