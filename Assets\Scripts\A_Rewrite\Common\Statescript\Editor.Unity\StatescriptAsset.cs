#if UNITY_EDITOR
using UnityEngine;
using GP.Common.Statescript;
using GP.Common.Statescript.Serialization;

namespace GP.Common.Statescript.Editor
{
    /// <summary>
    /// ScriptableObject asset for storing Statescript graphs in Unity
    /// </summary>
    [CreateAssetMenu(fileName = "New Statescript", menuName = "Statescript/Statescript Graph", order = 1)]
    public class StatescriptAsset : ScriptableObject
    {
        [SerializeField]
        private byte[] _serializedData;

        [SerializeField]
        private StatescriptSerializationFormat _serializationFormat = StatescriptSerializationFormat.Binary;

        [SerializeField]
        private string _graphName = "New Graph";

        [SerializeField]
        private string _description = "";

        [SerializeField]
        private int _version = 1;

        [SerializeField]
        private StatescriptEditorData _editorData = new();

        /// <summary>
        /// The cached graph instance (runtime only)
        /// </summary>
        [System.NonSerialized]
        private StatescriptGraph _cachedGraph;

        /// <summary>
        /// Get the graph name
        /// </summary>
        public string GraphName => _graphName;

        /// <summary>
        /// Get the graph description
        /// </summary>
        public string Description => _description;

        /// <summary>
        /// Get the serialization format
        /// </summary>
        public StatescriptSerializationFormat SerializationFormat => _serializationFormat;

        /// <summary>
        /// Get the version
        /// </summary>
        public int Version => _version;

        /// <summary>
        /// Check if the asset has valid data
        /// </summary>
        public bool HasData => _serializedData != null && _serializedData.Length > 0;

        /// <summary>
        /// Get the size of the serialized data
        /// </summary>
        public int DataSize => _serializedData?.Length ?? 0;

        /// <summary>
        /// Get the editor data for this asset
        /// </summary>
        public StatescriptEditorData EditorData => _editorData;

        /// <summary>
        /// Load the graph from the asset
        /// </summary>
        public StatescriptGraph LoadGraph()
        {
            if (_cachedGraph != null)
                return _cachedGraph;

            if (!HasData)
            {
                Debug.LogWarning($"Statescript asset '{name}' has no data");
                return null;
            }

            try
            {
                _cachedGraph = StatescriptSerializer.Deserialize(_serializedData, _serializationFormat);
                if (_cachedGraph != null)
                {
                    _cachedGraph.Name = _graphName;
                    _cachedGraph.Description = _description;
                }
                return _cachedGraph;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Failed to load Statescript graph from asset '{name}': {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Save a graph to the asset
        /// </summary>
        public void SaveGraph(StatescriptGraph graph)
        {
            if (graph == null)
            {
                Debug.LogError("Cannot save null graph");
                return;
            }

            try
            {
                _serializedData = StatescriptSerializer.Serialize(graph, _serializationFormat);
                _graphName = graph.Name;
                _description = graph.Description;
                _version++;
                _cachedGraph = null; // Clear cache to force reload

                UnityEditor.EditorUtility.SetDirty(this);
                Debug.Log($"Saved Statescript graph '{_graphName}' to asset '{name}'");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Failed to save Statescript graph to asset '{name}': {ex.Message}");
            }
        }

        /// <summary>
        /// Clear the cached graph
        /// </summary>
        public void ClearCache()
        {
            _cachedGraph = null;
        }

        /// <summary>
        /// Change the serialization format and re-serialize
        /// </summary>
        public void ChangeSerializationFormat(StatescriptSerializationFormat newFormat)
        {
            if (_serializationFormat == newFormat)
                return;

            var graph = LoadGraph();
            if (graph != null)
            {
                _serializationFormat = newFormat;
                SaveGraph(graph);
            }
        }

        /// <summary>
        /// Get serialization statistics
        /// </summary>
        public SerializationStats GetSerializationStats()
        {
            var graph = LoadGraph();
            if (graph != null)
            {
                return StatescriptSerializer.GetSerializationStats(graph);
            }
            return new SerializationStats { Error = "No graph data" };
        }

        /// <summary>
        /// Validate the graph data
        /// </summary>
        public bool ValidateData()
        {
            if (!HasData)
                return false;

            return StatescriptSerializer.ValidateSerializedData(_serializedData, _serializationFormat);
        }

        /// <summary>
        /// Create a new empty graph
        /// </summary>
        public void CreateNewGraph()
        {
            var graph = new StatescriptGraph(_graphName);
            graph.Description = _description;

            // Add an entry node by default
            var entryNode = new EntryNode();
            entryNode.Id = 1;
            entryNode.Name = "Entry";
            graph.AddNode(entryNode);

            SaveGraph(graph);
        }

        private void OnValidate()
        {
            // Ensure graph name is not empty
            if (string.IsNullOrEmpty(_graphName))
            {
                _graphName = name;
            }
        }

        private void Reset()
        {
            _graphName = name;
            _description = "";
            _serializationFormat = StatescriptSerializationFormat.Binary;
            _version = 1;
        }
    }
}
#endif
